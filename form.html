<style>
/* Container styling */
.contact-section {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    padding: 2rem;
    justify-content: center;
    align-items: flex-start;
    background: #f7f9fc;
    border-radius: 16px;
}

/* Left support box */
.support-box {
    flex: 1;
    min-width: 280px;
    max-width: 400px;
    background: #ffffff;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.05);
}

.support-box h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 1rem;
}

.support-box p {
    font-size: 0.95rem;
    color: #4a5568;
    margin: 0.75rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Right form box */
.form-box {
    flex: 2;
    min-width: 300px;
    max-width: 600px;
    background: #ffffff;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.05);
}

/* FluentForm elements */
.form-box .ff-el-group {
    margin-bottom: 1rem;
}

.form-box .ff-el-input--label label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #374151;
    font-size: 0.9rem;
}

.form-box .ff-el-form-control {
    width: 100%;
    padding: 0.9rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: #fff;
}

.form-box .ff-el-form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

/* Submit button */
.form-box .ff-btn {
    padding: 0.9rem 2rem;
    font-size: 1rem;
    background: #4a90e2;
    color: #fff;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: background 0.3s ease, transform 0.2s ease;
    width: auto;
}

.form-box .ff-btn:hover {
    background: #3b7dd8;
    transform: translateY(-2px);
}

/* Legacy form elements (fallback) */
.form-box input:not(.ff-el-form-control),
.form-box select:not(.ff-el-form-control),
.form-box textarea:not(.ff-el-form-control) {
    width: 100%;
    padding: 0.9rem 1rem;
    margin-bottom: 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.form-box input:not(.ff-el-form-control):focus,
.form-box select:not(.ff-el-form-control):focus,
.form-box textarea:not(.ff-el-form-control):focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

</style>

<!-- reCAPTCHA Script -->
<script src="https://www.google.com/recaptcha/api.js" async defer></script>

<script>
// Form submission handler with reCAPTCHA
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('.frm-fluent-form');
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            // Get reCAPTCHA response
            const recaptchaResponse = grecaptcha.getResponse();
            if (!recaptchaResponse) {
                alert('Please complete the reCAPTCHA verification.');
                return;
            }

            // Set the reCAPTCHA response in the hidden field
            document.querySelector('input[name="g-recaptcha-response"]').value = recaptchaResponse;

            // Submit the form
            this.submit();
        });
    }
});

// reCAPTCHA callback functions
function onRecaptchaSuccess() {
    console.log('reCAPTCHA verified successfully');
}

function onRecaptchaExpired() {
    console.log('reCAPTCHA expired');
    grecaptcha.reset();
}
</script>

<div class="contact-section">
  <div class="support-box">
    <h3>We are here to support!</h3>
    <p>If you have a question or would like to discuss how we can assist you, contact us below.</p>
    <p>📞 +6016-936 9733</p>
    <p>📧 <EMAIL></p>
    <p>📧 <EMAIL></p>
  </div>

  <div class="form-box">
    <form method="post" action="/wp-admin/admin-ajax.php" class="frm-fluent-form ff-form-loaded" data-form_id="3" enctype="multipart/form-data">
      <div class="ff-el-group">
        <div class="ff-el-input--label">
          <label for="ff_3_name">Name *</label>
        </div>
        <div class="ff-el-input--content">
          <input type="text" name="names[name]" id="ff_3_name" class="ff-el-form-control" placeholder="Name" required />
        </div>
      </div>

      <div class="ff-el-group">
        <div class="ff-el-input--label">
          <label for="ff_3_email">Email Address *</label>
        </div>
        <div class="ff-el-input--content">
          <input type="email" name="names[email]" id="ff_3_email" class="ff-el-form-control" placeholder="Email Address" required />
        </div>
      </div>

      <div class="ff-el-group">
        <div class="ff-el-input--label">
          <label for="ff_3_phone">Contact Number</label>
        </div>
        <div class="ff-el-input--content">
          <input type="text" name="names[phone]" id="ff_3_phone" class="ff-el-form-control" placeholder="Contact Number (Optional)" />
        </div>
      </div>

      <div class="ff-el-group">
        <div class="ff-el-input--label">
          <label for="ff_3_topic">Topic *</label>
        </div>
        <div class="ff-el-input--content">
          <select name="names[topic]" id="ff_3_topic" class="ff-el-form-control" required>
            <option value="">- Select a topic -</option>
            <option value="Lab Testing">Lab Testing</option>
            <option value="Product">Product</option>
            <option value="Lab Setup & Consultancy">Lab Setup & Consultancy</option>
            <option value="Calibration, Repair & Service">Calibration, Repair & Service</option>
            <option value="Training">Training</option>
          </select>
        </div>
      </div>

      <div class="ff-el-group">
        <div class="ff-el-input--label">
          <label for="ff_3_message">Message</label>
        </div>
        <div class="ff-el-input--content">
          <textarea name="names[message]" id="ff_3_message" class="ff-el-form-control" placeholder="Write Your Message Here (if any)" rows="4"></textarea>
        </div>
      </div>

      <!-- reCAPTCHA field -->
      <div class="ff-el-group">
        <div class="ff-el-input--content">
          <div class="g-recaptcha" data-sitekey="YOUR_RECAPTCHA_SITE_KEY"></div>
        </div>
      </div>

      <!-- FluentForm required fields -->
      <input type="hidden" name="action" value="fluentform_submit" />
      <input type="hidden" name="form_id" value="3" />
      <input type="hidden" name="_fluentform_3_fluentformnonce" value="" />
      <input type="hidden" name="_wp_http_referer" value="/contact-us/" />
      <input type="hidden" name="g-recaptcha-response" value="" />

      <div class="ff-el-group">
        <button type="submit" class="ff-btn ff-btn-submit ff-btn-md ff_btn_style">Send Message</button>
      </div>
    </form>
  </div>
</div>
